{"$schema": "https://biomejs.dev/schemas/2.0.5/schema.json", "extends": ["ultracite"], "files": {"includes": ["**", "!src/components/plasmic/**/*", "!src/app/(payload)/**/*", "!src/payload-types.ts", "!next-sitemap.config.cjs"]}, "formatter": {"lineWidth": 120, "indentStyle": "space", "indentWidth": 2}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}}}