{"name": "sp-cms-blank", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"prebuild": "next telemetry disable", "build": "cross-env NODE_OPTIONS=--no-deprecation next build --turbo", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "docker compose up", "dev:container": "cross-env NODE_OPTIONS=--no-deprecation --inspect=0.0.0.0:9229 next dev", "dev:local": "docker compose up mongo mongo-express -d && cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:clean": "rm -rf .next && pnpm dev", "db:reset": "docker compose exec mongo mongosh --eval 'db.dropDatabase()' senergy", "db:seed": "docker compose exec payload pnpm run db:seed:internal", "db:seed:internal": "cross-env NODE_OPTIONS=--no-deprecation tsx ./src/scripts/seed-database.ts", "docker:stop": "docker compose down", "docker:clean": "docker compose down -v", "docker:shell": "docker compose exec payload sh", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "email:dev": "email dev", "email:build": "email build", "email:export": "email export", "lint": "biome check .", "format": "biome format . --write", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@payloadcms/db-mongodb": "^3.43.0", "@payloadcms/email-resend": "^3.43.0", "@payloadcms/next": "^3.43.0", "@payloadcms/payload-cloud": "^3.43.0", "@payloadcms/plugin-sentry": "^3.43.0", "@payloadcms/richtext-lexical": "^3.43.0", "@payloadcms/ui": "^3.43.0", "@plasmicapp/host": "^1.0.222", "@plasmicapp/react-web": "^0.2.393", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.2", "@sentry/nextjs": "^9.31.0", "@subframe/core": "1.143.0", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "effect": "^3.16.9", "embla-carousel-react": "^8.6.0", "graphql": "^16.11.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "mongodb": "^6.17.0", "motion": "^12.19.1", "nanoid": "^5.1.5", "next": "^15.3.4", "next-themes": "^0.4.6", "payload": "^3.43.0", "posthog-js": "^1.255.1", "posthog-node": "^5.1.1", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "rough-notation": "^0.5.1", "sharp": "^0.34.2", "simple-icons": "^15.3.0", "slug": "^11.0.0", "sonner": "^2.0.5", "tailwindcss-animate": "^1.0.7", "typeit-react": "^2.7.8", "validator": "^13.15.15", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@types/lodash": "^4.17.18", "@types/node": "^24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/slug": "^5.0.9", "@types/validator": "^13.15.2", "autoprefixer": "^10.4.21", "babel-plugin-react-compiler": "19.1.0-rc.1", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "next-sitemap": "^4.2.3", "postcss": "^8.5.6", "react-email": "^4.0.16", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.17", "theo": "^8.1.5", "tsx": "^4.20.3", "typescript": "^5.8.3", "ultracite": "^5.0.12"}, "engines": {"node": "^18.20.2 || >=20.9.0"}, "packageManager": "pnpm@10.12.4", "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@sentry/cli", "core-js", "esbuild", "highlight.js", "sharp", "typeit"]}, "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss,md,mdx}": ["npx ultracite format"]}}