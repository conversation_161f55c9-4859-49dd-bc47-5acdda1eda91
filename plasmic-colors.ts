import {camelCase} from 'lodash';
import tokens from './src/components/plasmic/plasmic-tokens.theo.json' with {type: 'json'};

// Define an index signature for the color object
const colors: {[key: string]: string} = {};

for (const token of tokens.props) {
  if (token.type && token.type.toLowerCase() === 'color') {
    const key = camelCase(token.name);
    colors[key] = token.value;
  }
}

export default colors;
