import type { Access } from 'payload';
import type { User } from '@/payload-types';
import { isAdmin } from './is-admin';

export const isAdminOrSelf: Access = async ({ req, data }) => {
  if (!req.user) {
    return false;
  }

  // Check if user is admin first
  const isAdminResult = await isAdmin({ req });
  if (isAdminResult) {
    return true;
  }

  // For create operations, data will be undefined
  if (!data) {
    return true; // Allow creation, other access controls will handle restrictions
  }

  // Check if user is the owner
  const userId = (req.user as User).id;
  return data.owner === userId || (typeof data.owner === 'object' && data.owner?.id === userId);
};

// Reusable access pattern for Payload collections
// Users can only access their own data unless they are an admin
export const adminOrSelfAccess: Access = async ({ req, id }) => {
  if (!req.user) {
    return false;
  }

  // Check if user is admin
  const isAdminResult = await isAdmin({ req });
  if (isAdminResult) {
    return true;
  }

  // Allow users to read their own data
  // If no ID is provided (e.g., for list endpoints), deny access for non-admins
  return id ? req.user.id === id : false;
};
