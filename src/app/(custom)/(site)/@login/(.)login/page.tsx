'use client';

import {useRouter} from 'next/navigation';
import {useState} from 'react';
import AuthDialog from '@/components/ui/Auth/Dialog/index';

export default function Page() {
  const router = useRouter();
  const [open, setOpen] = useState(true);

  return (
    <AuthDialog
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) {
          // Wait for the animation duration (200ms from the Dialog CSS)
          setTimeout(() => {
            router.back();
          }, 200);
        }
      }}
      open={open}
    />
  );
}