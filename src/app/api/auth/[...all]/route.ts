import {to<PERSON><PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>} from 'better-auth/next-js';
import type {NextRequest} from 'next/server';
import {auth} from '@/auth/server';

const handler = toNextJsHandler(auth);

export const POST = async (req: NextRequest) => {
  const headers = new Headers();
  for (const [key, value] of req.headers.entries()) {
    headers.set(key, value);
  }

  const body = await req.text();
  const request = new Request(req.url, {
    method: req.method,
    headers,
    body,
  });

  const response = await handler.POST(request);
  return new Response(response.body, {
    status: response.status,
    headers: response.headers,
  });
};

export const GET = async (req: NextRequest) => {
  const headers = new Headers();
  for (const [key, value] of req.headers.entries()) {
    headers.set(key, value);
  }

  const request = new Request(req.url, {
    method: req.method,
    headers,
  });

  const response = await handler.GET(request);
  return new Response(response.body, {
    status: response.status,
    headers: response.headers,
  });
};