import { revalidatePath } from 'next/cache';
import { type NextRequest, NextResponse } from 'next/server';
import { getPayloadClient } from '@/lib/payload';

// This is a secret token to prevent unauthorized revalidation
const REVALIDATION_KEY = process.env.REVALIDATION_KEY;

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Get the secret token from the request
    const secret = request.headers.get('x-revalidation-secret');

    // Check if the secret is valid
    if (!REVALIDATION_KEY || secret !== REVALIDATION_KEY) {
      return NextResponse.json({ message: 'Invalid revalidation secret' }, { status: 401 });
    }

    // Get the profile ID from the request
    const { id } = body;

    if (!id) {
      return NextResponse.json({ message: 'Profile ID is required' }, { status: 400 });
    }

    // Optional: Verify that the profile exists and is published
    try {
      const payload = await getPayloadClient();
      const profile = await payload.findByID({
        collection: 'profile',
        id,
        overrideAccess: true,
      });

      if (!profile) {
        return NextResponse.json({ message: 'Profile not found' }, { status: 404 });
      }
    } catch (verifyError) {
      throw new Error('Error verifying profile existence:', {
        cause: verifyError,
      });
    }

    // Revalidate the profile page and the profiles list page
    revalidatePath(`/profile/${id}`);
    revalidatePath('/profile');

    return NextResponse.json(
      {
        message: 'Successfully revalidated',
        revalidated: true,
        paths: [`/profile/${id}`, '/profile'],
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json({ message: 'Error revalidating', error: (error as Error).message }, { status: 500 });
  }
}
