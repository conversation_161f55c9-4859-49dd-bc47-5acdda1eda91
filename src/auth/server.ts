import {betterAuth, type BetterAuthOptions} from 'better-auth';
import {mongodbAdapter} from 'better-auth/adapters/mongodb';
import {nextCookies} from 'better-auth/next-js';
import {emailOTP, magicLink, openAPI} from 'better-auth/plugins';
import {admin as adminPlugin} from 'better-auth/plugins/admin';
import {passkey} from 'better-auth/plugins/passkey';
import {MongoClient} from 'mongodb';
import {sendAuthEmail} from '@/utils/send-auth-email';
import {ac, admin, superadmin, user, waitlisted} from './permissions';

if (!process.env.DATABASE_URI) {
  throw new Error('DATABASE_URI environment variable is not defined');
}

if (!process.env.BETTER_AUTH_URL) {
  throw new Error('BETTER_AUTH_URL environment variable is not defined');
}

const client = new MongoClient(process.env.DATABASE_URI);
const db = client.db();

const options = {
  database: mongodbAdapter(db),
  appName: process.env.APP_NAME ?? 'Spherical CMS',
  baseURL: process.env.BETTER_AUTH_URL,
  basePath: '/api/auth',
  secret: process.env.BETTER_AUTH_SECRET,
  trustedOrigins: [
    process.env.BETTER_AUTH_URL,
    `${process.env.BETTER_AUTH_URL}/api/auth/`,
  ],

  user: {
    modelName: 'users',
    deleteUser: {
      enabled: true,
    },
  },
  emailAndPassword: {
    enabled: false,
    requireEmailVerification: false,
  },
  socialProviders: {
    linkedin: {
      enabled: true,
      clientId: process.env.LINKEDIN_CLIENT_ID ?? '',
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET ?? '',
      redirectURI: `${process.env.BETTER_AUTH_URL}/api/auth/callback/linkedin`,
      scope: ['openid', 'profile', 'email'],
    },
  },
  session: {
    modelName: 'auth-sessions',
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // Cache auth session 5 minutes
    },
  },
  account: {
    modelName: 'auth-accounts',
    accountLinking: {
      enabled: true,
      allowDifferentEmails: false,
    },
  },
  verification: {
    modelName: 'auth-verifications',
  },
  plugins: [
    passkey({
      origin: process.env.BETTER_AUTH_URL,
      rpName: process.env.APP_NAME ?? 'Spherical CMS',
      rpID: process.env.APP_NAME ?? 'Spherical CMS',
    }),
    openAPI({
      disableDefaultReference: process.env.NODE_ENV !== 'development',
    }),
    adminPlugin({
      adminRoles: ['admin', 'superadmin'],
      defaultRole: 'waitlisted',
      ac,
      roles: {
        superadmin,
        admin,
        user,
        waitlisted,
      },
    }),
    magicLink({
      sendMagicLink: async ({ email, url }) => {
        await sendAuthEmail({ email, url });
      },
      rateLimit: {
        window: 300,
        max: 6,
      },
    }),
    emailOTP({
      sendVerificationOTP: async ({ email }) => {
        await sendAuthEmail({ email, url: '' });
      },
    }),
  ],
  databaseHooks: {
    user: {
      create: {},
    },
  },
  advanced: {
    cookiePrefix: 'spherical-auth',
  },
} satisfies BetterAuthOptions;

export const auth = betterAuth({
  ...options,
  plugins: [
    ...(options.plugins ?? []),
    nextCookies(), // Must always be the last plugin in the array
  ],
});