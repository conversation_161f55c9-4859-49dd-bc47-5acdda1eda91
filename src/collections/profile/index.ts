import type { CollectionConfig } from 'payload';
import slug from 'slug';
import validator from 'validator';
import { z } from 'zod';
import { isAdmin } from '@/access/is-admin';
import { isAdminOrSelf } from '@/access/is-admin-or-self';
import { zodToPayload } from '@/utils/zod-to-payload';
import { certificationFields } from './certifications';
import { educationFields } from './education';
import { experienceFields } from './experience';
import { languagesFields } from './languages';
import { licensesFields } from './licenses';
import { patentsFields } from './patents';
import { publicationsFields } from './publications';
import { trademarksFields } from './trademarks';
//----------------------------------

export const Profile: CollectionConfig = {
  slug: 'profile',
  admin: {
    useAsTitle: 'preferred_name',
    group: 'Platform',
    preview: ({ slug }) => {
      return `${process.env.SITE_URL}/profile/${slug}`;
    },
  },
  versions: {
    drafts: {
      autosave: true,
    },
  },
  access: {
    read: isAdminOrSelf,
    update: isAdminOrSelf,
    delete: isAdmin,
    create: isAdmin,
  },

  hooks: {
    afterChange: [
      async ({ doc, operation }) => {
        if (doc._status === 'published') {
          try {
            const revalidationUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/api/revalidate/profile`;
            const revalidationSecret = process.env.REVALIDATION_KEY;
            if (!revalidationSecret) {
              return;
            }
            await fetch(revalidationUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'x-revalidation-secret': revalidationSecret,
              },
              body: JSON.stringify({
                id: doc.id,
                operation,
              }),
            });
          } catch (error) {
            throw new Error('Error triggering revalidation:', {
              cause: error,
            });
          }
        }
      },
    ],
  },

  fields: [
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      unique: true,
      validate: zodToPayload(
        z.string().refine(validator.isSlug, {
          message: 'Slug must only contain letters, numbers, dashes, and underscores.',
        })
      ),
      defaultValue: async ({ user, req: { payload } }) => {
        const baseSlug = slug(user?.name || '', '');

        // Check if the slug already exists
        let isUnique = false;
        let finalSlug = baseSlug;
        let attempts = 0;

        while (!isUnique && attempts < 10) {
          const existing = await payload.find({
            collection: 'profile',
            where: {
              slug: {
                equals: finalSlug,
              },
            },
          });

          if (existing.totalDocs === 0) {
            isUnique = true;
          } else {
            // Generate a random 3-digit number
            const randomSuffix = Math.floor(Math.random() * 900 + 100);
            finalSlug = `${baseSlug}-${randomSuffix}`;
            attempts++;
          }
        }

        return finalSlug;
      },
    },
    {
      name: 'owner',
      label: 'Owner',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
      unique: true,
      defaultValue: ({ user }) => user?.id,
    },
    {
      name: 'profile_photo',
      label: 'Profile Photo',
      type: 'relationship',
      relationTo: 'media',
      required: false,
      hasMany: false,
    },
    {
      name: 'preferred_name',
      label: 'Preferred Name',
      type: 'text',
      required: true,
      defaultValue: ({ user }) => user?.name,
    },
    {
      type: 'row',
      fields: [
        {
          name: 'location',
          label: 'Country',
          type: 'text',
        },
        {
          name: 'default_timezone',
          label: 'Default Timezone',
          type: 'select',
          required: false,
          options: [
            {
              label: 'America/New_York',
              value: 'America/New_York',
            },
            {
              label: 'America/Chicago',
              value: 'America/Chicago',
            },
            {
              label: 'America/Los_Angeles',
              value: 'America/Los_Angeles',
            },
          ],
        },
      ],
    },
    {
      type: 'row',
      fields: [
        {
          name: 'start_time',
          label: 'Start Time',
          type: 'date',
          required: false,
          admin: {
            date: {
              pickerAppearance: 'timeOnly',
            },
          },
        },
        {
          name: 'end_time',
          label: 'End Time',
          type: 'date',
          required: false,
          admin: {
            date: {
              pickerAppearance: 'timeOnly',
            },
          },
        },
      ],
    },
    {
      type: 'row',
      fields: [
        {
          name: 'pitch_intro',
          label: 'Pitch Intro',
          type: 'text',
          required: false,
        },
        {
          name: 'primary_role',
          label: 'Primary Role',
          type: 'text',
          required: false,
        },
        {
          name: 'pitch_descriptor',
          label: 'Pitch Descriptor',
          type: 'text',
          required: false,
        },
      ],
    },
    {
      name: 'introduction_bio',
      label: 'Introduction Bio',
      type: 'textarea',
      required: false,
      admin: {
        description: "Introduction Bio for the user's profile overview page",
      },
    },
    {
      name: 'full_bio',
      label: 'Full Bio',
      type: 'textarea',
      required: false,
      admin: {
        description: "Full Bio for the user's profile about page",
      },
    },

    //----------------------------------
    {
      type: 'tabs',
      tabs: [
        {
          label: 'About',
          fields: [
            {
              name: 'education',
              type: 'array',
              label: 'Education',
              fields: educationFields,
            },
            {
              name: 'experience',
              type: 'array',
              label: 'Experience',
              fields: experienceFields,
            },
            {
              name: 'languages',
              type: 'array',
              label: 'Languages',
              fields: languagesFields,
            },
            {
              name: 'publications',
              type: 'array',
              label: 'Publications',
              fields: publicationsFields,
            },
            {
              name: 'licenses',
              type: 'array',
              label: 'Licenses',
              fields: licensesFields,
            },
            {
              name: 'certifications',
              type: 'array',
              label: 'Certifications',
              fields: certificationFields,
            },
            {
              name: 'patents',
              type: 'array',
              label: 'Patents',
              fields: patentsFields,
            },
            {
              name: 'trademarks',
              type: 'array',
              label: 'Trademarks',
              fields: trademarksFields,
            },
          ],
        },
        {
          label: 'Case Studies',
          fields: [
            {
              name: 'case_studies',
              label: 'Case Studies',
              type: 'join',
              collection: 'case-studies',
              on: 'owner',
              hasMany: true,
            },
          ],
        },
      ],
    },
  ],
};
