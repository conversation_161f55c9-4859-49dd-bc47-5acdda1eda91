import { Body, But<PERSON>, Container, Head, Heading, Html, Preview, Section, Text } from '@react-email/components';

interface LoginEmailProps {
  magicLink: string;
  appName?: string;
  userEmail?: string;
}

export const LoginEmail = ({ magicLink, appName, userEmail }: LoginEmailProps) => {
  const previewText = `Sign in to ${appName}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>Sign in to {appName}</Heading>

          <Section style={buttonContainer}>
            <Text style={text}>Click the button below to securely sign in to your account on this device.</Text>
            <Button href={magicLink} style={button}>
              Continue to {appName}
            </Button>
            <Text style={instructionText}>This link will expire in 5 minutes</Text>
          </Section>

          {/*           <Hr style={hr} />

          <Section>
            <Text style={text}>
              Alternatively, you may enter this verification code in the login
              form to sign in:
            </Text>
            <Text style={codeText}>{otp}</Text>
          </Section>

          <Hr style={hr} /> */}

          <Text style={footer}>
            This sign in attempt was made for {userEmail}. If this wasn't you, please ignore this email.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

const main = {
  backgroundColor: '#ffffff',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '40px 24px',
  maxWidth: '600px',
};

const heading = {
  fontSize: '24px',
  fontWeight: '600',
  color: '#000000',
  padding: '0',
  margin: '0 0 24px 0',
  textAlign: 'left' as const,
};

const buttonContainer = {
  padding: '24px 0',
};

const button = {
  backgroundColor: '#000000',
  borderRadius: '4px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: '500',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 16px',
  margin: '0',
};

const text = {
  color: '#000000',
  fontSize: '14px',
  lineHeight: '20px',
  textAlign: 'left' as const,
  margin: '0 0 16px 0',
};

const _codeText = {
  fontSize: '32px',
  fontWeight: '500',
  textAlign: 'left' as const,
  letterSpacing: '4px',
  color: '#000000',
  margin: '16px 0',
  fontFamily: 'monospace',
};

const instructionText = {
  color: '#666666',
  fontSize: '14px',
  lineHeight: '20px',
  textAlign: 'left' as const,
};

const _hr = {
  borderColor: '#e5e5e5',
  margin: '24px 0',
};

const footer = {
  color: '#666666',
  fontSize: '14px',
  lineHeight: '20px',
  textAlign: 'left' as const,
};

export default LoginEmail;
