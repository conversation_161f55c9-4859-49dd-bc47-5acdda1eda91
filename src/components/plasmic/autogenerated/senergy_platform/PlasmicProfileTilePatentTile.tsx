/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: R0Gfd7-8fcvf

"use client";

import * as React from "react";
import {useRouter} from "next/navigation";

import {
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  Flex as Flex__,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  PlasmicIcon as PlasmicIcon__,
  SingleBooleanChoiceArg,
  Stack as Stack__,
  StrictProps,
  useDollarState
} from "@plasmicapp/react-web";
import {useDataEnv} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component
import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTilePatentTile.module.css"; // plasmic-import: R0Gfd7-8fcvf/css
import PatentedIcon, {PatentedIcon} from "./icons/PlasmicIcon__Patented"; // plasmic-import: 0zRPSen3c0Ql/icon
import HashtagIcon, {HashtagIcon} from "./icons/PlasmicIcon__Hashtag"; // plasmic-import: 7fTFQT6QY_o8/icon
import GlobeIcon from "./icons/PlasmicIcon__Globe"; // plasmic-import: 4U-HfnBQfBTq/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import PatentPendingIcon from "./icons/PlasmicIcon__PatentPending"; // plasmic-import: 7cetWB1_lXa5/icon
import UtilitiesIcon from "./icons/PlasmicIcon__Utilities";
import {usePlasmicDataOp} from "@plasmicapp/data-sources"; // plasmic-import: 3SU3Z2EeP247/icon

createPlasmicElementProxy;

export type PlasmicProfileTilePatentTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
};
export type PlasmicProfileTilePatentTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
};
type VariantPropType = keyof PlasmicProfileTilePatentTile__VariantsArgs;
export const PlasmicProfileTilePatentTile__VariantProps =
  new Array<VariantPropType>("editable", "overview");

export type PlasmicProfileTilePatentTile__ArgsType = {
  patentId?: string;
  titleInputValue?: string;
  countryOfRegistrationInputValue?: string;
  eventDateInputValue?: string;
  registrationNumberInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onCountryOfRegistrationInputValueChange?: (val: string) => void;
  onEventDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
};
type ArgPropType = keyof PlasmicProfileTilePatentTile__ArgsType;
export const PlasmicProfileTilePatentTile__ArgProps = new Array<ArgPropType>(
  "patentId",
  "titleInputValue",
  "countryOfRegistrationInputValue",
  "eventDateInputValue",
  "registrationNumberInputValue",
  "descriptionInputValue",
  "onTitleInputValueChange",
  "onCountryOfRegistrationInputValueChange",
  "onEventDateInputValueChange",
  "onRegistrationNumberInputValueChange",
  "onDescriptionInputValueChange",
  "deleteButtonClickStage",
  "onDeleteButtonClickStageChange",
  "deleteButtonDisabled",
  "onDeleteButtonDisabledChange"
);

export type PlasmicProfileTilePatentTile__OverridesType = {
  patentSpacingContainer?: Flex__<"div">;
  patentIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  titleInput?: Flex__<typeof SubcomponentTextInput>;
  infoBar?: Flex__<"section">;
  registrationNumberInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  countryOfRegistration?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  eventDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  statusSelectorInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  patentTypeSelectorInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot5?: Flex__<"svg">;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTilePatentTileProps {
  patentId?: string;
  titleInputValue?: string;
  countryOfRegistrationInputValue?: string;
  eventDateInputValue?: string;
  registrationNumberInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onCountryOfRegistrationInputValueChange?: (val: string) => void;
  onEventDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTilePatentTile__RenderFunc(props: {
  variants: PlasmicProfileTilePatentTile__VariantsArgs;
  args: PlasmicProfileTilePatentTile__ArgsType;
  overrides: PlasmicProfileTilePatentTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "titleInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "titleInputValue",
        onChangeProp: "onTitleInputValueChange"
      },
      {
        path: "countryOfRegistration.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "countryOfRegistrationInputValue",
        onChangeProp: "onCountryOfRegistrationInputValueChange"
      },
      {
        path: "eventDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "eventDateInputValue",
        onChangeProp: "onEventDateInputValueChange"
      },
      {
        path: "registrationNumberInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "registrationNumberInputValue",
        onChangeProp: "onRegistrationNumberInputValueChange"
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "statusSelectorInput.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "patentTypeSelectorInput.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "pendingUpdates",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "titleInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"patentSpacingContainer"}
      data-plasmic-override={overrides.patentSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.patentSpacingContainer,
        {
          [sty.patentSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.patentSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <PatentedIcon
        data-plasmic-name={"patentIcon"}
        data-plasmic-override={overrides.patentIcon}
        className={classNames(projectcss.all, sty.patentIcon, {
          [sty.patentIconeditable]: hasVariant($state, "editable", "editable"),
          [sty.patentIconoverview]: hasVariant($state, "overview", "overview")
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"titleInput"}
          data-plasmic-override={overrides.titleInput}
          className={classNames("__wab_instance", sty.titleInput, {
            [sty.titleInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleInputoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.titleInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "titleInput",
            "errorMessage"
          ])}
          inputHoverText={"Patent Name"}
          inputName={"Patent Name"}
          inputPlaceholder={"Patent Name"}
          inputValue={generateStateValueProp($state, ["titleInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "titleInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["titleInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <Stack__
          as={"section"}
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          hasGap={true}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.registrationNumberInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationNumberInput"}
              data-plasmic-override={overrides.registrationNumberInput}
              className={classNames(
                "__wab_instance",
                sty.registrationNumberInput,
                {
                  [sty.registrationNumberInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationNumberInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.registrationNumberInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <HashtagIcon
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  className={classNames(projectcss.all, sty.iconSpot3, {
                    [sty.iconSpot3editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Number"}
              inputName={"Registration Number"}
              inputPlaceholder={"Registration Number"}
              inputValue={generateStateValueProp($state, [
                "registrationNumberInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationNumberInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.countryOfRegistration.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"countryOfRegistration"}
              data-plasmic-override={overrides.countryOfRegistration}
              className={classNames(
                "__wab_instance",
                sty.countryOfRegistration,
                {
                  [sty.countryOfRegistrationeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.countryOfRegistrationoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.countryOfRegistration.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <GlobeIcon
                  data-plasmic-name={"iconSpot"}
                  data-plasmic-override={overrides.iconSpot}
                  className={classNames(projectcss.all, sty.iconSpot, {
                    [sty.iconSpoteditable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Location of Registration"}
              inputName={"Location of Registration"}
              inputPlaceholder={"Location of Registration"}
              inputValue={generateStateValueProp($state, [
                "countryOfRegistration",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "countryOfRegistration",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.eventDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"eventDateInput"}
              data-plasmic-override={overrides.eventDateInput}
              className={classNames("__wab_instance", sty.eventDateInput, {
                [sty.eventDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.eventDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.eventDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2, {
                    [sty.iconSpot2editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Event Date"}
              inputName={"Event Date"}
              inputPlaceholder={"Event Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "eventDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "eventDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"statusSelectorInput"}
              data-plasmic-override={overrides.statusSelectorInput}
              className={classNames("__wab_instance", sty.statusSelectorInput, {
                [sty.statusSelectorInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.statusSelectorInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return undefined;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              dropdownName={"Status"}
              dropdownOptions={(() => {
                const __composite = [
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null }
                ];
                __composite["0"]["value"] = "Pending";
                __composite["1"]["value"] = "Patented";
                __composite["2"]["value"] = "Abandoned";
                __composite["3"]["value"] = "Expired";
                return __composite;
              })()}
              dropdownPlaceholderText={"Status"}
              editable={
                hasVariant($state, "overview", "overview")
                  ? undefined
                  : hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <PlasmicIcon__
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  PlasmicIconType={
                    PatentPendingIcon
                  }
                  className={classNames(projectcss.all, sty.iconSpot4, {
                    [sty.iconSpot4editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Status"}
              inputName={"Status"}
              inputPlaceholder={"Status"}
              inputValue={generateStateValueProp($state, [
                "statusSelectorInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "statusSelectorInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "overview", "overview") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"patentTypeSelectorInput"}
              data-plasmic-override={overrides.patentTypeSelectorInput}
              className={classNames(
                "__wab_instance",
                sty.patentTypeSelectorInput,
                {
                  [sty.patentTypeSelectorInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.patentTypeSelectorInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={
                hasVariant($state, "editable", "editable")
                  ? (() => {
                      try {
                        return undefined;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                  : (() => {
                      try {
                        return undefined;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
              }
              dropdownName={"Patent Type"}
              dropdownOptions={(() => {
                const __composite = [
                  { value: null },
                  { value: null },
                  { value: null }
                ];
                __composite["0"]["value"] = "Design";
                __composite["1"]["value"] = "Utility";
                __composite["2"]["value"] = "Plant";
                return __composite;
              })()}
              dropdownPlaceholderText={"Patent Type"}
              editable={
                hasVariant($state, "overview", "overview")
                  ? undefined
                  : hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <PlasmicIcon__
                  data-plasmic-name={"iconSpot5"}
                  data-plasmic-override={overrides.iconSpot5}
                  PlasmicIconType={
                    UtilitiesIcon
                  }
                  className={classNames(projectcss.all, sty.iconSpot5, {
                    [sty.iconSpot5editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Patent Type"}
              inputName={"Patent Type"}
              inputPlaceholder={"Patent Type"}
              inputValue={generateStateValueProp($state, [
                "patentTypeSelectorInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "patentTypeSelectorInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
        </Stack__>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.descriptionoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return $state.description.value.length > 160
                      ? $state.description.value.substring(0, 160) + "..."
                      : $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : (() => {
                  try {
                    return $state.description.value.length > 160
                      ? $state.description.value.substring(0, 160) + "..."
                      : $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
          }
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={
            hasVariant($state, "editable", "editable")
              ? "Patent Summary"
              : undefined
          }
          inputName={
            hasVariant($state, "editable", "editable")
              ? "Patent Summary"
              : undefined
          }
          inputPlaceholder={
            hasVariant($state, "editable", "editable")
              ? "Patent Summary"
              : undefined
          }
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  patentSpacingContainer: [
    "patentSpacingContainer",
    "patentIcon",
    "informationStack",
    "titleInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistration",
    "iconSpot",
    "eventDateInput",
    "iconSpot2",
    "statusSelectorInput",
    "iconSpot4",
    "patentTypeSelectorInput",
    "iconSpot5",
    "description",
    "subDeleteButton"
  ],
  patentIcon: ["patentIcon"],
  informationStack: [
    "informationStack",
    "titleInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistration",
    "iconSpot",
    "eventDateInput",
    "iconSpot2",
    "statusSelectorInput",
    "iconSpot4",
    "patentTypeSelectorInput",
    "iconSpot5",
    "description"
  ],
  titleInput: ["titleInput"],
  infoBar: [
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistration",
    "iconSpot",
    "eventDateInput",
    "iconSpot2",
    "statusSelectorInput",
    "iconSpot4",
    "patentTypeSelectorInput",
    "iconSpot5"
  ],
  registrationNumberInput: ["registrationNumberInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  countryOfRegistration: ["countryOfRegistration", "iconSpot"],
  iconSpot: ["iconSpot"],
  eventDateInput: ["eventDateInput", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  statusSelectorInput: ["statusSelectorInput", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  patentTypeSelectorInput: ["patentTypeSelectorInput", "iconSpot5"],
  iconSpot5: ["iconSpot5"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  patentSpacingContainer: "div";
  patentIcon: "svg";
  informationStack: "div";
  titleInput: typeof SubcomponentTextInput;
  infoBar: "section";
  registrationNumberInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  countryOfRegistration: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  eventDateInput: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  statusSelectorInput: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  patentTypeSelectorInput: typeof SubcomponentIconWithText;
  iconSpot5: "svg";
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTilePatentTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTilePatentTile__VariantsArgs;
    args?: PlasmicProfileTilePatentTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTilePatentTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTilePatentTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTilePatentTile__ArgProps,
          internalVariantPropNames: PlasmicProfileTilePatentTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTilePatentTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "patentSpacingContainer") {
    func.displayName = "PlasmicProfileTilePatentTile";
  } else {
    func.displayName = `PlasmicProfileTilePatentTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTilePatentTile = Object.assign(
  // Top-level PlasmicProfileTilePatentTile renders the root element
  makeNodeComponent("patentSpacingContainer"),
  {
    // Helper components rendering sub-elements
    patentIcon: makeNodeComponent("patentIcon"),
    informationStack: makeNodeComponent("informationStack"),
    titleInput: makeNodeComponent("titleInput"),
    infoBar: makeNodeComponent("infoBar"),
    registrationNumberInput: makeNodeComponent("registrationNumberInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    countryOfRegistration: makeNodeComponent("countryOfRegistration"),
    iconSpot: makeNodeComponent("iconSpot"),
    eventDateInput: makeNodeComponent("eventDateInput"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    statusSelectorInput: makeNodeComponent("statusSelectorInput"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    patentTypeSelectorInput: makeNodeComponent("patentTypeSelectorInput"),
    iconSpot5: makeNodeComponent("iconSpot5"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTilePatentTile
    internalVariantProps: PlasmicProfileTilePatentTile__VariantProps,
    internalArgProps: PlasmicProfileTilePatentTile__ArgProps
  }
);

export default PlasmicProfileTilePatentTile;
/* prettier-ignore-end */