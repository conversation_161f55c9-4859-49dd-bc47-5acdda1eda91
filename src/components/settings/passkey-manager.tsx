import {Key, Loader2, Trash} from 'lucide-react';
import {useCallback, useEffect, useState} from 'react';
import {toast} from 'sonner';
import {authClient} from '@/auth/client';
import {Button} from '@/components/ui/button';
import {ProfileSection} from './profile-section';

interface Passkey {
  id: string;
  name?: string;
  lastUsed?: Date;
  createdAt: Date;
}

export function PasskeyManager() {
  const [passkeys, setPasskeys] = useState<Passkey[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchPasskeys = useCallback(async () => {
    try {
      const response = await authClient.passkey.listUserPasskeys();
      if (response.data) {
        setPasskeys(response.data);
      }
    } catch (_error) {
      toast.error(
        'Failed to load passkeys. Please refresh the page to try again.'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPasskeys();
  }, [fetchPasskeys]);

  const handleRegisterPasskey = async () => {
    try {
      const response = await authClient.passkey.addPasskey({});
      if (!response?.data || response.error) {
        throw new Error(
          response?.error?.message ?? 'Failed to register passkey'
        );
      }
      await fetchPasskeys();
      toast.success('Passkey registered successfully');
    } catch (_error) {
      toast.error('Failed to register passkey. Please try again.');
    }
  };

  const handleDeletePasskey = async (passkey: Passkey) => {
    const promise = authClient.passkey
      .deletePasskey({ id: passkey.id })
      .then(() => fetchPasskeys());

    toast.promise(promise, {
      loading: 'Deleting passkey...',
      success: 'Passkey deleted successfully',
      error: 'Failed to delete passkey. Please try again.',
    });

    try {
      await promise;
    } catch (_error) {
      // Error is handled by toast.promise
    }
  };

  const PasskeyContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      );
    }

    if (passkeys.length > 0) {
      return (
        <div className="space-y-4">
          {passkeys.map((passkey) => (
            <div
              className="flex items-center justify-between rounded-lg border p-4"
              key={passkey.id}
            >
              <div className="space-y-1">
                <p className="font-medium text-sm">{passkey.name}</p>
                <p className="text-muted-foreground text-sm">
                  Added on {new Date(passkey.createdAt).toLocaleDateString()}
                </p>
              </div>
              <Button
                onClick={() => handleDeletePasskey(passkey)}
                size="icon"
                variant="ghost"
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      );
    }

    return (
      <p className="text-muted-foreground text-sm">
        You haven&apos;t set up any passkeys yet.
      </p>
    );
  };

  return (
    <ProfileSection
      description="Manage your passkeys for passwordless authentication"
      title="Passkeys"
    >
      <div className="space-y-4">
        <PasskeyContent />
        <Button
          className="w-full gap-2"
          onClick={handleRegisterPasskey}
          variant="outline"
        >
          <Key size={16} />
          Register New Passkey
        </Button>
      </div>
    </ProfileSection>
  );
}