import {zod<PERSON><PERSON>olver} from '@hookform/resolvers/zod';
import {Loader2} from 'lucide-react';
import {useState} from 'react';
import {useForm} from 'react-hook-form';
import {toast} from 'sonner';
import * as z from 'zod';
import {authClient} from '@/auth/client';
import {Button} from '@/components/ui/button';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage,} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {ProfileSection} from './profile-section';

const profileFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

interface ProfileInformationProps {
  email: string;
  initialName: string;
}

export function ProfileInformation({
  email,
  initialName,
}: ProfileInformationProps) {
  const [loading, setLoading] = useState(false);
  const [currentServerName, setCurrentServerName] = useState(initialName);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: initialName,
      email,
    },
  });

  const handleUpdateProfile = async (values: ProfileFormValues) => {
    if (values.name.trim() === currentServerName.trim()) {
      toast.info('No changes to save');
      return;
    }

    setLoading(true);
    const promise = authClient.updateUser({ name: values.name });

    toast.promise(promise, {
      loading: 'Updating your profile...',
      success: 'Profile updated successfully',
      error: 'Failed to update profile. Please try again.',
    });

    try {
      await promise;
      setCurrentServerName(values.name);
    } catch (_error) {
      // Error is handled by toast.promise
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProfileSection
      description="Manage your account details and preferences"
      title="Profile Information"
    >
      <Form {...form}>
        <form
          className="space-y-4"
          onSubmit={form.handleSubmit(handleUpdateProfile)}
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input {...field} disabled type="email" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input {...field} type="text" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button disabled={loading} type="submit">
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </form>
      </Form>
    </ProfileSection>
  );
}