import type React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ProfileSectionProps {
  title: string;
  description: string;
  children: React.ReactNode;
  variant?: 'default' | 'destructive';
}

export function ProfileSection({ title, description, children, variant = 'default' }: ProfileSectionProps) {
  return (
    <Card className={variant === 'destructive' ? 'border-destructive' : ''}>
      <CardHeader>
        <CardTitle className={variant === 'destructive' ? 'text-destructive' : ''}>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}
