import {formatDistanceToNow} from 'date-fns';
import {Clock, Globe, Loader2, LogOut} from 'lucide-react';
import {useEffect, useState} from 'react';
import {toast} from 'sonner';
import {authClient} from '@/auth/client';
import {Button} from '../ui/button';
import {CardContent} from '../ui/card';
import {ProfileSection} from './profile-section';

// Define the shape of the session object from API response
interface SessionResponse {
  id: string;
  current?: boolean;
  lastActive?: string;
  device?: string;
  browser?: string;
  location?: string;
  user?: unknown; // User information associated with the session
  session?: string; // Session identifier
  token?: string; // Alternative token identifier
  [key: string]: unknown;
}

export function Sessions() {
  const [sessions, setSessions] = useState<SessionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [revoking, setRevoking] = useState<string | null>(null);

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        const response = await authClient.listSessions();
        if (response.data) {
          // Identify current session and enhance with metadata
          const enhancedSessions = response.data.map(
            (session: SessionResponse) => ({
              ...session,
              current: session.current ?? false,
              lastActive: session.lastActive ?? new Date().toISOString(),
            })
          );
          setSessions(enhancedSessions);
        }
      } catch (_error) {
        toast.error(
          'Failed to load sessions. Please refresh the page to try again.'
        );
      } finally {
        setLoading(false);
      }
    };
    fetchSessions();
  }, []);

  const handleRevokeSession = async (sessionId: string) => {
    try {
      setRevoking(sessionId);

      const response = await authClient.revokeSession({ token: sessionId });

      // Check if the response indicates success (no error property)
      if (response && !('error' in response)) {
        setSessions(
          sessions.filter(
            (session) =>
              session.session !== sessionId && session.id !== sessionId
          )
        );
        toast.success('Session revoked successfully');
      } else {
        throw new Error('Failed to revoke session');
      }
    } catch (_error) {
      toast.error('Failed to revoke session');

      // Don't update the sessions list if revocation failed
    } finally {
      setRevoking(null);
    }
  };

  // Try to find the correct token field from the session object
  const getSessionToken = (session: SessionResponse): string => {
    // Try different potential token fields
    return session.token ?? session.session ?? session.id;
  };

  return (
    <ProfileSection description="Manage your active sessions" title="Sessions">
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : sessions.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">
            No active sessions found
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            {sessions.map((session) => {
              const sessionToken = getSessionToken(session);
              return (
                <div
                  className="flex items-center justify-between rounded-lg border p-4"
                  key={session.id}
                >
                  <div className="flex items-start gap-3">
                    <Globe className="mt-0.5 h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="flex items-center font-medium">
                        {session.browser ?? 'Unknown device'}
                        {session.current && (
                          <span className="ml-2 rounded-full bg-primary px-2 py-0.5 text-primary-foreground text-xs">
                            Current
                          </span>
                        )}
                      </div>
                      {session.lastActive && (
                        <div className="flex items-center gap-1 text-muted-foreground text-sm">
                          <Clock className="h-3 w-3" />
                          <span>
                            Last active{' '}
                            {formatDistanceToNow(new Date(session.lastActive), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      )}
                      {session.location && (
                        <div className="text-muted-foreground text-sm">
                          {session.location}
                        </div>
                      )}
                      <div className="mt-1 text-muted-foreground text-xs opacity-50">
                        Token: {sessionToken.substring(0, 8)}...
                      </div>
                    </div>
                  </div>
                  <Button
                    disabled={session.current || revoking === sessionToken}
                    onClick={() =>
                      !session.current && handleRevokeSession(sessionToken)
                    }
                    size="sm"
                    variant="destructive"
                  >
                    {revoking === sessionToken ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <LogOut className="mr-2 h-4 w-4" />
                        {session.current ? 'Current' : 'Revoke'}
                      </>
                    )}
                  </Button>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </ProfileSection>
  );
}