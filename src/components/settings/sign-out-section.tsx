import {Loader2} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {ProfileSection} from './profile-section';

interface SignOutSectionProps {
  onSignOut: () => Promise<void>;
  loading: boolean;
}

export function SignOutSection({ onSignOut, loading }: SignOutSectionProps) {
  return (
    <ProfileSection
      description="Sign out from your account on this device"
      title="Sign Out"
    >
      <Button
        className="gap-2"
        disabled={loading}
        onClick={onSignOut}
        variant="destructive"
      >
        {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        Sign Out
      </Button>
    </ProfileSection>
  );
}