'use client';
import { Button, TextInput, toast } from '@payloadcms/ui';
import type React from 'react';
import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useAuthRedirect, useMagicLink } from '@/auth/hooks';
// Import shared auth utilities
import { type AuthLoadingType, sendMagicLink, socialSignIn } from '@/auth/service';
import { formatTimeLeft } from '@/auth/utils';
import { SendPaperPlaneIcon } from '@/components/plasmic/autogenerated/senergy_platform/icons/PlasmicIcon__SendPaperPlane';

// Styles
const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    placeItems: 'stretch',
    maxWidth: '400px',
    width: '100%',
    margin: '0 auto',
  },
  centeredContainer: {
    display: 'flex',
    flexDirection: 'column',
    placeItems: 'stretch',
    maxWidth: '400px',
    width: '100%',
    margin: '0 auto',
    textAlign: 'center',
    gap: '16px',
  },
  iconContainer: {
    margin: '20px auto',
    width: '48px',
    height: '48px',
    borderRadius: '50%',
    background: 'rgba(0, 125, 255, 0.1)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  heading: {
    fontWeight: 'bold',
    fontSize: '20px',
  },
  text: {
    color: '#666',
  },
  smallText: {
    color: '#666',
    fontSize: '14px',
  },
  timerContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
  },
  errorText: {
    color: 'red',
    fontSize: '14px',
    marginTop: '4px',
  },
  formLayout: {
    display: 'flex',
    flexDirection: 'column',
    justifyItems: 'stretch',
  },
  divider: {
    margin: '20px 0',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
  },
  dividerLine: {
    flex: 1,
    height: '1px',
    background: '#e0e0e0',
  },
  dividerText: {
    color: '#666',
    fontSize: '14px',
  },
  loadingContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
} as const;

// Animations
const Animations = () => (
  <style global jsx>{`
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    @keyframes pulse {
      0% { opacity: 0.5; }
      50% { opacity: 1; }
      100% { opacity: 0.5; }
    }
    .pulse-dot {
      animation: pulse 2s infinite;
    }
  `}</style>
);

// Reusable components
const LoadingSpinner = () => (
  <span
    aria-hidden="true"
    className="loading-spinner"
    style={{
      display: 'inline-block',
      width: '16px',
      height: '16px',
      border: '2px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '50%',
      borderTopColor: 'white',
      animation: 'spin 1s linear infinite',
    }}
  />
);

const Divider = () => (
  <div style={styles.divider}>
    <div style={styles.dividerLine} />
    <span style={styles.dividerText}>or</span>
    <div style={styles.dividerLine} />
  </div>
);

const LinkedInIcon = () => (
  <svg
    aria-hidden="true"
    height="16"
    style={{ marginRight: '8px' }}
    viewBox="0 0 24 24"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93zM6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37z"
      fill="currentColor"
    />
  </svg>
);

const PulseDot = () => (
  <span
    aria-hidden="true"
    className="pulse-dot"
    style={{
      display: 'inline-block',
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      background: '#007DFF',
    }}
  />
);

// Email validation schema
const emailSchema = z.string().email('Please enter a valid email address');

// MagicLinkSent component to display when a magic link has been sent
const MagicLinkSentView = ({
  isExpired,
  sentEmail,
  secondsLeft,
  loadingType,
  onResendLink,
  onUseDifferentEmail,
}: {
  isExpired: boolean;
  sentEmail: string;
  secondsLeft: number;
  loadingType: AuthLoadingType;
  onResendLink: () => void;
  onUseDifferentEmail: () => void;
}) => (
  <div style={styles.centeredContainer}>
    <Animations />
    <div style={styles.iconContainer}>
      <SendPaperPlaneIcon style={{ width: '24px', height: '24px', color: 'currentColor' }} />
    </div>
    <h3 style={styles.heading}>{isExpired ? 'Link expired' : 'Check your email'}</h3>
    <p style={styles.text}>
      {isExpired ? (
        'Your magic link has expired.'
      ) : (
        <>
          We've sent a magic link to <span style={{ fontWeight: 'bold' }}>{sentEmail}</span>
        </>
      )}
    </p>
    {!isExpired && (
      <p style={styles.timerContainer}>
        <PulseDot />
        <span style={styles.smallText}>Link expires in {formatTimeLeft(secondsLeft)}</span>
      </p>
    )}

    {isExpired ? (
      <Button
        buttonStyle="primary"
        disabled={loadingType === 'email'}
        onClick={onResendLink}
        size="large"
        type="button"
      >
        {loadingType === 'email' ? (
          <div style={styles.loadingContainer}>
            <LoadingSpinner />
            <span>Sending new link...</span>
          </div>
        ) : (
          'Resend magic link'
        )}
      </Button>
    ) : (
      secondsLeft < 60 && (
        <Button
          buttonStyle="secondary"
          disabled={loadingType === 'email'}
          onClick={onResendLink}
          size="large"
          type="button"
        >
          {loadingType === 'email' ? (
            <div style={styles.loadingContainer}>
              <LoadingSpinner />
              <span>Sending new link...</span>
            </div>
          ) : (
            'Resend magic link'
          )}
        </Button>
      )
    )}

    <Button buttonStyle={isExpired ? 'secondary' : 'pill'} onClick={onUseDifferentEmail} size="large" type="button">
      Use a different email
    </Button>
  </div>
);

// EmailForm component for email sign-in
const EmailForm = ({
  email,
  emailError,
  loadingType,
  onEmailChange,
  onSubmit,
}: {
  email: string;
  emailError: string | null;
  loadingType: AuthLoadingType;
  onEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
}) => (
  <form aria-label="Authentication form" onSubmit={onSubmit}>
    <div style={styles.formLayout}>
      <TextInput
        aria-describedby={emailError ? 'email-error' : undefined}
        aria-invalid={!!emailError}
        label="Email"
        onChange={onEmailChange}
        path="email"
        readOnly={loadingType !== null}
        required
        showError={!!emailError}
        value={email}
      />
      {emailError && (
        <div id="email-error" role="alert" style={styles.errorText}>
          {emailError}
        </div>
      )}
      <Button
        aria-busy={loadingType === 'email'}
        buttonStyle="primary"
        disabled={loadingType !== null || !!emailError}
        size="large"
        type="submit"
      >
        {loadingType === 'email' ? (
          <div style={styles.loadingContainer}>
            <LoadingSpinner />
            <span>Sending magic link...</span>
          </div>
        ) : (
          'Continue with Email'
        )}
      </Button>
    </div>
  </form>
);

// LinkedIn button component
const LinkedInButton = ({ loadingType, onSignIn }: { loadingType: AuthLoadingType; onSignIn: () => Promise<void> }) => (
  <Button
    aria-busy={loadingType === 'linkedin'}
    buttonStyle="primary"
    disabled={loadingType !== null}
    onClick={onSignIn}
    size="large"
    type="button"
  >
    {loadingType === 'linkedin' ? (
      <div style={styles.loadingContainer}>
        <LoadingSpinner />
        <span>Signing in...</span>
      </div>
    ) : (
      <>
        <LinkedInIcon />
        Continue with LinkedIn
      </>
    )}
  </Button>
);

export default function AuthForm() {
  const [loadingType, setLoadingType] = useState<AuthLoadingType>(null);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);

  // Enable cross-tab authentication detection
  useAuthRedirect();

  // Use the shared magic link hook
  const { magicLinkSent, sentEmail, isExpired, secondsLeft, clearMagicLink, syncWithStorage } = useMagicLink();

  // Validate email as user types
  useEffect(() => {
    if (email.trim() === '') {
      setEmailError(null);
      return;
    }

    // Debounce validation
    const timer = setTimeout(() => {
      const result = emailSchema.safeParse(email);
      if (result.success) {
        setEmailError(null);
      } else {
        setEmailError(result.error.errors[0].message);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [email]);

  const handleLinkedInSignIn = async () => {
    await socialSignIn({
      provider: 'linkedin',
      callbackURL: window.location.href,
      errorCallbackURL: window.location.href,
      newUserCallbackURL: window.location.href,
      onRequest: () => setLoadingType('linkedin'),
      onError: () => setLoadingType(null),
    });
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Final validation before submission
    const result = emailSchema.safeParse(email);
    if (!result.success) {
      setEmailError(result.error.errors[0].message);
      return;
    }

    handleEmailSignIn();
  };

  const handleEmailSignIn = () => {
    sendMagicLink({
      email,
      callbackURL: window.location.href,
      onRequest: () => setLoadingType('email'),
      onSuccess: () => {
        setLoadingType(null);
        // Sync with localStorage to update our UI state
        syncWithStorage();
      },
      onError: () => setLoadingType(null),
    });
  };

  const handleResendLink = () => {
    if (sentEmail) {
      setEmail(sentEmail);
      sendMagicLink({
        email: sentEmail,
        callbackURL: window.location.href,
        onRequest: () => setLoadingType('email'),
        onSuccess: () => {
          setLoadingType(null);
          // Sync with localStorage to update our UI state
          syncWithStorage();
          toast.success('New magic link sent', {
            description: `Check your email at ${sentEmail}`,
          });
        },
        onError: () => setLoadingType(null),
      });
    }
  };

  const handleUseDifferentEmail = () => {
    clearMagicLink();
    setEmail('');
  };

  if (magicLinkSent) {
    return (
      <MagicLinkSentView
        isExpired={isExpired}
        loadingType={loadingType}
        onResendLink={handleResendLink}
        onUseDifferentEmail={handleUseDifferentEmail}
        secondsLeft={secondsLeft}
        sentEmail={sentEmail}
      />
    );
  }

  return (
    <div style={styles.container}>
      <EmailForm
        email={email}
        emailError={emailError}
        loadingType={loadingType}
        onEmailChange={handleEmailChange}
        onSubmit={handleEmailSubmit}
      />
      <Animations />
      <Divider />
      <LinkedInButton loadingType={loadingType} onSignIn={handleLinkedInSignIn} />
    </div>
  );
}
