'use client';

/*
 * Documentation:
 * Bar Chart — https://app.subframe.com/library?component=Bar+Chart_4d4f30e7-1869-4980-8b96-617df3b37912
 */

import * as SubframeCore from '@subframe/core';
import React from 'react';
import * as SubframeUtils from '../utils';

interface BarChartRootProps extends React.ComponentProps<typeof SubframeCore.BarChart> {
  stacked?: boolean;
  className?: string;
}

const BarChartRoot = React.forwardRef<HTMLElement, BarChartRootProps>(function BarChartRoot(
  { stacked = false, className, ...otherProps }: BarChartRootProps,
  ref
) {
  return (
    <SubframeCore.BarChart
      className={SubframeUtils.twClassNames('h-80 w-full', className)}
      colors={['#22c55e', '#bbf7d0', '#16a34a', '#86efac', '#15803d', '#4ade80']}
      ref={ref as any}
      stacked={stacked}
      {...otherProps}
    />
  );
});

export const BarChart = BarChartRoot;
