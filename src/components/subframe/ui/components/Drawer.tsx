'use client';

/*
 * Documentation:
 * Drawer — https://app.subframe.com/library?component=Drawer_1e71b2cb-8d72-4e67-b368-8805179e9444
 */

import * as SubframeCore from '@subframe/core';
import React from 'react';
import * as SubframeUtils from '../utils';

interface ContentProps extends React.ComponentProps<typeof SubframeCore.Drawer.Content> {
  children?: React.ReactNode;
  className?: string;
}

const Content = React.forwardRef<HTMLElement, ContentProps>(function Content(
  { children, className, ...otherProps }: ContentProps,
  ref
) {
  return children ? (
    <SubframeCore.Drawer.Content asChild={true} {...otherProps}>
      <div
        className={SubframeUtils.twClassNames(
          'flex h-full min-w-[320px] flex-col items-start gap-2 border-neutral-border border-l border-solid bg-default-background',
          className
        )}
        ref={ref as any}
      >
        {children}
      </div>
    </SubframeCore.Drawer.Content>
  ) : null;
});

interface DrawerRootProps extends React.ComponentProps<typeof SubframeCore.Drawer.Root> {
  children?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
}

const DrawerRoot = React.forwardRef<HTMLElement, DrawerRootProps>(function DrawerRoot(
  { children, className, ...otherProps }: DrawerRootProps,
  ref
) {
  return children ? (
    <SubframeCore.Drawer.Root asChild={true} {...otherProps}>
      <div
        className={SubframeUtils.twClassNames(
          'flex h-full w-full flex-col items-end justify-center gap-2 bg-[#00000066]',
          className
        )}
        ref={ref as any}
      >
        {children}
      </div>
    </SubframeCore.Drawer.Root>
  ) : null;
});

export const Drawer = Object.assign(DrawerRoot, {
  Content,
});
