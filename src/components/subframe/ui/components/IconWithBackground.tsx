'use client';

/*
 * Documentation:
 * Icon with background — https://app.subframe.com/library?component=Icon+with+background_c5d68c0e-4c0c-4cff-8d8c-6ff334859b3a
 */

import * as SubframeCore from '@subframe/core';
import { FeatherCheck } from '@subframe/core';
import React from 'react';
import * as SubframeUtils from '../utils';

interface IconWithBackgroundRootProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'brand' | 'neutral' | 'error' | 'success' | 'warning';
  size?: 'x-large' | 'large' | 'medium' | 'small' | 'x-small';
  icon?: React.ReactNode;
  square?: boolean;
  className?: string;
}

const IconWithBackgroundRoot = React.forwardRef<HTMLElement, IconWithBackgroundRootProps>(
  function IconWithBackgroundRoot(
    {
      variant = 'brand',
      size = 'x-small',
      icon = <FeatherCheck />,
      square = false,
      className,
      ...otherProps
    }: IconWithBackgroundRootProps,
    ref
  ) {
    return (
        <div
            className={SubframeUtils.twClassNames(
                "group/c5d68c0e flex h-5 w-5 items-center justify-center gap-2 rounded-full bg-brand-100",
                {
                    "rounded-md": square,
                    "h-6 w-6": size === "small",
                    "h-8 w-8": size === "medium",
                    "h-12 w-12": size === "large",
                    "h-16 w-16": size === "x-large",
                    "bg-warning-100": variant === "warning",
                    "bg-success-100": variant === "success",
                    "bg-error-100": variant === "error",
                    "bg-neutral-100": variant === "neutral",
                },
                className
            )}
            ref={ref as any}
            {...otherProps}
        >
            {icon ? (
                <SubframeCore.IconWrapper
                    className={SubframeUtils.twClassNames(
                        'font-['Inter'] font-[400] text-[10px] text-brand-800 leading-[12px]',
                        {
                            'font-caption text-caption': size === "small",
                            'font-body text-body': size === "medium",
                            'font-heading-2 text-heading-2': size === "large",
                            'font-heading-1 text-heading-1': size === "x-large",
                            "text-warning-800": variant === "warning",
                            "text-success-800": variant === "success",
                            "text-error-800": variant === "error",
                            "text-neutral-700": variant === "neutral",
                        }
                    )}
                >
                    {icon}
                </SubframeCore.IconWrapper>
            ) : null}
        </div>
    );
  }
);

export const IconWithBackground = IconWithBackgroundRoot;
