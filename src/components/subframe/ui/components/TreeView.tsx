'use client';

/*
 * Documentation:
 * Tree View — https://app.subframe.com/library?component=Tree+View_4ed46422-ecc3-41e8-8787-e55ee10cdc75
 */

import * as SubframeCore from '@subframe/core';
import { FeatherFile, FeatherFolder } from '@subframe/core';
import React from 'react';
import * as SubframeUtils from '../utils';
import { Accordion } from './Accordion';

interface FolderProps extends React.ComponentProps<typeof Accordion> {
  children?: React.ReactNode;
  label?: React.ReactNode;
  icon?: React.ReactNode;
  className?: string;
}

const Folder = React.forwardRef<HTMLElement, FolderProps>(function Folder(
  { children, label, icon = <FeatherFolder />, className, ...otherProps }: FolderProps,
  ref
) {
  return (
    <Accordion
      className={SubframeUtils.twClassNames('group/c841484c cursor-pointer', className)}
      defaultOpen={true}
      ref={ref as any}
      trigger={
        <div className="flex w-full items-center gap-2 rounded-md px-3 py-2 group-hover/c841484c:bg-neutral-50">
          {icon ? (
            <SubframeCore.IconWrapper className="font-body text-body text-default-font">
              {icon}
            </SubframeCore.IconWrapper>
          ) : null}
          {label ? (
            <span className="line-clamp-1 shrink-0 grow basis-0 font-body text-body text-default-font">{label}</span>
          ) : null}
          <Accordion.Chevron />
        </div>
      }
      {...otherProps}
    >
      {children ? <div className="flex w-full flex-col items-start gap-1 pt-1 pl-6">{children}</div> : null}
    </Accordion>
  );
});

interface ItemProps extends React.HTMLAttributes<HTMLDivElement> {
  selected?: boolean;
  label?: React.ReactNode;
  icon?: React.ReactNode;
  className?: string;
}

const Item = React.forwardRef<HTMLElement, ItemProps>(function Item(
  { selected = false, label, icon = <FeatherFile />, className, ...otherProps }: ItemProps,
  ref
) {
  return (
    <div
      className={SubframeUtils.twClassNames(
        'group/42786044 flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-2 hover:bg-neutral-50',
        { 'bg-brand-100 hover:bg-brand-100': selected },
        className
      )}
      ref={ref as any}
      {...otherProps}
    >
      {icon ? (
        <SubframeCore.IconWrapper
          className={SubframeUtils.twClassNames('font-body text-body text-default-font', {
            'text-brand-700': selected,
          })}
        >
          {icon}
        </SubframeCore.IconWrapper>
      ) : null}
      {label ? (
        <span
          className={SubframeUtils.twClassNames(
            'line-clamp-1 shrink-0 grow basis-0 font-body text-body text-default-font',
            { 'text-brand-700': selected }
          )}
        >
          {label}
        </span>
      ) : null}
    </div>
  );
});

interface TreeViewRootProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

const TreeViewRoot = React.forwardRef<HTMLElement, TreeViewRootProps>(function TreeViewRoot(
  { children, className, ...otherProps }: TreeViewRootProps,
  ref
) {
  return children ? (
    <div
      className={SubframeUtils.twClassNames('flex w-full flex-col items-start', className)}
      ref={ref as any}
      {...otherProps}
    >
      {children}
    </div>
  ) : null;
});

export const TreeView = Object.assign(TreeViewRoot, {
  Folder,
  Item,
});
