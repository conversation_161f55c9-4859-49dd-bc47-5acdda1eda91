'use client';

import type React from 'react';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {annotate, annotationGroup} from 'rough-notation';
import type {BracketType, RoughAnnotation, RoughAnnotationGroup, RoughAnnotationType,} from 'rough-notation/lib/model';

// Configuration interface for both the hook and component
export interface HighlighterConfig {
  action?: RoughAnnotationType;
  color?: string;
  staggerDelay?: number;
  iterations?: number;
  animationDuration?: number;
  brackets?: BracketType | BracketType[];
  padding?: number | [number, number] | [number, number, number, number];
  animate?: boolean;
  strokeWidth?: number;
  multiline?: boolean;
  rtl?: boolean;
  selector?: string; // Added support for targeting by CSS selector
}

/**
 * Custom hook to apply rough notation highlighting to elements
 *
 * @param element The element to apply the annotation to
 * @param config Configuration for the annotation
 * @returns The annotation instance if created
 */
export function useHighlighter(
  element: HTMLElement | null,
  config: HighlighterConfig
): RoughAnnotation | null {
  const annotationRef = useRef<RoughAnnotation | null>(null);
  const {
    action = 'highlight',
    color = '#ffd1dc',
    iterations = 2,
    animationDuration = 500,
    brackets = 'left',
    padding,
    animate = true,
    strokeWidth,
    multiline = true,
    rtl,
  } = config;

  useEffect(() => {
    if (element) {
      // Clean up previous annotation if it exists
      if (annotationRef.current) {
        annotationRef.current.remove();
        annotationRef.current = null;
      }

      const annotation = annotate(element, {
        type: action,
        color,
        multiline,
        padding: padding ?? (action === 'circle' ? 8 : 2),
        iterations,
        animationDuration,
        animate,
        strokeWidth,
        rtl,
        ...(action === 'bracket' && { brackets }),
      });

      annotationRef.current = annotation;
      annotation.show();
    }

    return () => {
      if (annotationRef.current) {
        annotationRef.current.remove();
        annotationRef.current = null;
      }
    };
  }, [
    element,
    action,
    color,
    iterations,
    animationDuration,
    brackets,
    padding,
    animate,
    strokeWidth,
    multiline,
    rtl,
  ]);

  return annotationRef.current;
}

interface HighlighterProps extends HighlighterConfig {
  children?: React.ReactNode;
}

/**
 * Highlighter component that applies rough notation effects to children or elements matching a selector
 */
export default function Highlighter({
  children,
  action = 'highlight',
  color = '#ffd1dc',
  staggerDelay = 0,
  iterations = 2,
  animationDuration = 500,
  brackets = 'left',
  padding,
  animate = true,
  strokeWidth,
  multiline = true,
  rtl,
  selector,
}: HighlighterProps) {
  const [shouldShow, setShouldShow] = useState(staggerDelay === 0);
  const elementRef = useRef<HTMLSpanElement>(null);

  // Handle staggered appearance if delay is provided
  useEffect(() => {
    if (staggerDelay > 0) {
      const timer = setTimeout(() => {
        setShouldShow(true);
      }, staggerDelay);
      return () => clearTimeout(timer);
    }
  }, [staggerDelay]);

  // Create annotation configurations
  const annotationConfig = useMemo(
    () => ({
      type: action,
      color,
      multiline,
      padding: padding ?? (action === 'circle' ? 8 : 2),
      iterations,
      animationDuration,
      animate,
      strokeWidth,
      rtl,
      ...(action === 'bracket' && { brackets }),
    }),
    [
      action,
      color,
      multiline,
      padding,
      iterations,
      animationDuration,
      animate,
      strokeWidth,
      rtl,
      brackets,
    ]
  );

  // Create annotation for a single element
  const createAnnotation = useCallback(
    (element: HTMLElement) => {
      return annotate(element, annotationConfig);
    },
    [annotationConfig]
  );

  // Clean up annotations
  const cleanupAnnotations = useCallback((annotations: RoughAnnotation[]) => {
    for (const annotation of annotations) {
      annotation.remove();
    }
  }, []);

  // Handle showing annotations for multiple elements
  const showAnnotations = useCallback((annotations: RoughAnnotation[]) => {
    if (annotations.length > 1) {
      const group = annotationGroup(annotations);
      group.show();
    } else if (annotations.length === 1) {
      annotations[0].show();
    }
  }, []);

  // For selector-based targeting
  useEffect(() => {
    if (!(selector && shouldShow)) {
      return;
    }

    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) {
      return;
    }

    const annotations: RoughAnnotation[] = [];

    // Create annotations for all matching elements
    for (const element of elements) {
      const annotation = createAnnotation(element as HTMLElement);
      annotations.push(annotation);
    }

    // Show the annotations
    showAnnotations(annotations);

    // Cleanup on unmount or when dependencies change
    return () => cleanupAnnotations(annotations);
  }, [
    selector,
    shouldShow,
    createAnnotation,
    showAnnotations,
    cleanupAnnotations,
  ]);

  // For direct children (only when no selector is provided)
  useHighlighter(
    !selector && shouldShow ? elementRef.current : null,
    annotationConfig
  );

  // If using selector and no children, render nothing
  if (selector && !children) {
    return null;
  }

  return (
    <span className="relative inline-block bg-transparent" ref={elementRef}>
      {children}
    </span>
  );
}

/**
 * Group multiple highlighters to control them together
 */
export function HighlighterGroup({
  children,
  show = true,
}: {
  children: React.ReactNode;
  show?: boolean;
}) {
  const groupRef = useRef<RoughAnnotationGroup | null>(null);
  const annotationsRef = useRef<RoughAnnotation[]>([]);

  // This context will be used to collect annotations from child Highlighter components
  const [annotations, setAnnotations] = useState<RoughAnnotation[]>([]);

  // Create a context to collect annotations from children
  useEffect(() => {
    if (annotations.length > 0) {
      groupRef.current = annotationGroup(annotations);

      if (show) {
        groupRef.current.show();
      }
    }

    return () => {
      if (groupRef.current) {
        for (const annotation of annotations) {
          annotation.remove();
        }
      }
    };
  }, [annotations, show]);

  // Function to register annotations from children
  const _registerAnnotation = (annotation: RoughAnnotation) => {
    if (annotation && !annotationsRef.current.includes(annotation)) {
      annotationsRef.current.push(annotation);
      setAnnotations([...annotationsRef.current]);
    }
  };

  return (
    <div className="highlighter-group">
      {/* You would need a context provider here in a real implementation */}
      {children}
    </div>
  );
}