'use client';

import { useRouter } from 'next/navigation';
import { createContext, type ReactNode, useContext, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { authClient, type Session } from '@/auth/client';
import { clearMagicLinkData } from '@/auth/utils';

// Create a context for the session data
export const SessionContext = createContext<{
  user: NonNullable<Session['user']> | null;
  loading: boolean;
  signOut: () => Promise<void>;
  deleteAccount: () => Promise<void>;
}>({
  user: null,
  loading: true,
  signOut: async () => {
    /* This is intentionally empty as it will be implemented in the SessionProvider */
  },
  deleteAccount: async () => {
    /* This is intentionally empty as it will be implemented in the SessionProvider */
  },
});

// Custom hook to use the session data
export const useSession = () => useContext(SessionContext);

export function SessionProvider({ children }: { children: ReactNode }) {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<NonNullable<Session['user']> | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function fetchUserData() {
      try {
        const session = await authClient.getSession();
        if (session.data) {
          setUser(session.data.user);
          // Clear magic link data from localStorage when user is authenticated
          clearMagicLinkData();
        } else {
          // We let the middleware handle redirects
          setUser(null);
        }
      } catch (_error) {
        toast.error('Failed to load user data. Please try refreshing the page.');
        setUser(null);
      } finally {
        setLoading(false);
      }
    }

    fetchUserData();
  }, []);

  const signOut = async () => {
    setLoading(true);
    try {
      await authClient.signOut();
      // Clear magic link data when user signs out
      clearMagicLinkData();
      toast.success('Successfully signed out');
      router.push('/');
    } catch (_error) {
      toast.error('Failed to sign out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const deleteAccount = async () => {
    try {
      await authClient.deleteUser();
      toast.success('Your account has been permanently deleted');
      router.push('/');
    } catch (_error) {
      toast.error('Failed to delete account. Please try again or contact support.');
    }
  };

  return (
    <SessionContext.Provider
      value={{
        user,
        loading,
        signOut,
        deleteAccount,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}
