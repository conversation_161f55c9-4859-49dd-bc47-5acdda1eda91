import { MongoClient } from 'mongodb';

/**
 * Database seeding script - adds initial data for development
 *
 * Run with: pnpm run db:seed
 */
async function seed() {
  try {
    // Connect directly to MongoDB to bypass auth and access controls
    if (!process.env.DATABASE_URI) {
      throw new Error('DATABASE_URI environment variable is not defined');
    }

    const client = new MongoClient(process.env.DATABASE_URI);
    await client.connect();
    const db = client.db();

    // Check if admin user exists
    const usersCollection = db.collection('users');
    const existingAdmins = await usersCollection.countDocuments({
      role: 'admin',
    });

    // Create admin user if none exists
    if (existingAdmins === 0) {
      await usersCollection.insertOne({
        email: '<EMAIL>',
        emailVerified: true,
        name: 'Admin User',
        role: 'admin',
        banned: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    } else {
    }

    // Add more seed data as needed:
    // - Projects
    // - Settings
    // - Other collection data

    await client.close();
  } catch (_error) {
    process.exit(1);
  }

  process.exit(0);
}

// Run the seed function
seed();
