import {render} from '@react-email/components';
import {LoginEmail} from '@/components/email/login';
import {getPayloadClient} from '@/utils/payload';

async function sendAuthEmail({ email, url }: { email: string; url: string }) {
  const payload = await getPayloadClient();

  const html = await render(
    LoginEmail({
      magicLink: url,
      appName: process.env.APP_NAME ?? 'Spherical CMS',
      userEmail: email,
    }),
    {
      pretty: true,
    }
  );
  const text = await render(
    LoginEmail({
      magicLink: url,
      appName: process.env.APP_NAME ?? 'Spherical CMS',
      userEmail: email,
    }),
    {
      plainText: true,
    }
  );
  await payload.sendEmail({
    from: process.env.APP_EMAIL ?? '<EMAIL>',
    to: email,
    subject: process.env.APP_NAME ?? 'Spherical CMS - Magic Link',
    html,
    text,
  });
}

export { sendAuthEmail };