import type { Validate } from 'payload';
import type { z } from 'zod';

export const zodToPayload = <T>(schema: z.ZodType<T>): Validate => {
  return (value, { required }) => {
    if (value === undefined || value === null || value === '') {
      return required ? 'This field is required' : true;
    }
    const result = schema.safeParse(value);
    return result.success ? true : result.error.errors[0]?.message || 'Invalid input';
  };
};
